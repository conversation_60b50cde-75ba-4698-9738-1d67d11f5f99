'use client';

import { useState, useEffect } from 'react';
import { scriptSyncManager } from '@/lib/script-sync';
import { webScriptDB } from '@/lib/indexdb';

export default function TestScriptPage() {
  const [scripts, setScripts] = useState<any[]>([]);
  const [testCode, setTestCode] = useState(`<!DOCTYPE html>
<html>
<head>
  <title>Test Script</title>
</head>
<body>
  <h1>Hello World!</h1>
  <p>This is a test script.</p>
</body>
</html>`);

  const loadScripts = async () => {
    try {
      const allScripts = await webScriptDB.getAllScripts();
      setScripts(allScripts);
      console.log('All scripts:', allScripts);
    } catch (error) {
      console.error('Failed to load scripts:', error);
    }
  };

  const saveTestScript = async () => {
    try {
      await scriptSyncManager.saveScript({
        message_id: 'test-msg-123',
        displayCode: testCode,
        updated_at: Date.now(),
        miniapp_id: 'test-miniapp',
        name: 'Test Script',
        version: 1,
      });
      console.log('Test script saved');
      loadScripts();
    } catch (error) {
      console.error('Failed to save test script:', error);
    }
  };

  const clearAllScripts = async () => {
    try {
      const allScripts = await webScriptDB.getAllScripts();
      for (const script of allScripts) {
        await scriptSyncManager.deleteScript(script.message_id);
      }
      console.log('All scripts cleared');
      loadScripts();
    } catch (error) {
      console.error('Failed to clear scripts:', error);
    }
  };

  useEffect(() => {
    loadScripts();
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Script Storage Test</h1>

      <div style={{ marginBottom: '20px' }}>
        <h2>Test Script Code:</h2>
        <textarea
          value={testCode}
          onChange={e => setTestCode(e.target.value)}
          style={{
            width: '100%',
            height: '200px',
            fontFamily: 'monospace',
            padding: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px',
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={saveTestScript}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Save Test Script
        </button>

        <button
          onClick={loadScripts}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Reload Scripts
        </button>

        <button
          onClick={clearAllScripts}
          style={{
            padding: '10px 20px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Clear All Scripts
        </button>
      </div>

      <div>
        <h2>Stored Scripts ({scripts.length}):</h2>
        {scripts.length === 0 ? (
          <p>No scripts found</p>
        ) : (
          <div>
            {scripts.map((script, index) => (
              <div
                key={script.message_id}
                style={{
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  padding: '10px',
                  marginBottom: '10px',
                  backgroundColor: '#f9f9f9',
                }}
              >
                <h3>Script {index + 1}</h3>
                <p>
                  <strong>Message ID:</strong> {script.message_id}
                </p>
                <p>
                  <strong>Name:</strong> {script.name}
                </p>
                <p>
                  <strong>Version:</strong> {script.version}
                </p>
                <p>
                  <strong>Miniapp ID:</strong> {script.miniapp_id}
                </p>
                <p>
                  <strong>Updated:</strong> {new Date(script.updated_at).toLocaleString()}
                </p>
                <p>
                  <strong>Code Length:</strong> {script.displayCode.length} characters
                </p>
                <details>
                  <summary>View Code</summary>
                  <pre
                    style={{
                      backgroundColor: '#f5f5f5',
                      padding: '10px',
                      borderRadius: '4px',
                      overflow: 'auto',
                      maxHeight: '200px',
                      fontSize: '12px',
                    }}
                  >
                    {script.displayCode}
                  </pre>
                </details>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
