import { webScriptDB, Script } from './indexdb';

// Synchronization events
export interface ScriptSyncEvent {
  type: 'script-updated' | 'script-deleted';
  messageId: string;
  script?: Script;
  timestamp: number;
}

class ScriptSyncManager {
  private channel: BroadcastChannel | null = null;
  private listeners: Set<(event: ScriptSyncEvent) => void> = new Set();

  constructor() {
    // Try to create BroadcastChannel for cross-context communication
    try {
      this.channel = new BroadcastChannel('script-sync');
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
    } catch (error) {
      console.warn('BroadcastChannel not available, using fallback sync');
    }
  }

  private handleBroadcastMessage(event: MessageEvent<ScriptSyncEvent>) {
    this.notifyListeners(event.data);
  }

  private notifyListeners(event: ScriptSyncEvent) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  // Save script and notify other contexts
  async saveScript(script: Script): Promise<void> {
    script.updated_at = Date.now();
    await webScriptDB.saveScript(script);

    const event: ScriptSyncEvent = {
      type: 'script-updated',
      messageId: script.message_id,
      script,
      timestamp: Date.now(),
    };

    this.broadcastEvent(event);
  }

  // Delete script and notify other contexts
  async deleteScript(messageId: string): Promise<void> {
    await webScriptDB.deleteScript(messageId);

    const event: ScriptSyncEvent = {
      type: 'script-deleted',
      messageId,
      timestamp: Date.now(),
    };

    this.broadcastEvent(event);
  }

  // Get script from local storage
  async getScript(messageId: string): Promise<Script | null> {
    return await webScriptDB.getScript(messageId);
  }

  private broadcastEvent(event: ScriptSyncEvent) {
    if (this.channel) {
      try {
        this.channel.postMessage(event);
      } catch (error) {
        console.warn('Failed to broadcast sync event:', error);
      }
    }
  }

  // Listen for sync events from other contexts
  addSyncListener(listener: (event: ScriptSyncEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Cleanup
  destroy() {
    if (this.channel) {
      this.channel.close();
    }
    this.listeners.clear();
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
